"""
Configuration settings for the Keyword Rank Tracker application
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Project paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
EXPORTS_DIR = PROJECT_ROOT / "exports"

# Create directories if they don't exist
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)
EXPORTS_DIR.mkdir(exist_ok=True)

# API Configuration
APIFY_API_TOKEN = os.getenv("APIFY_API_TOKEN", "")
APIFY_ACTOR_ID = "scraperlink/google-search-results-serp-scraper"
APIFY_BASE_URL = "https://api.apify.com/v2"

# Database Configuration
DATABASE_PATH = os.getenv("DATABASE_PATH", str(DATA_DIR / "rank_tracker.db"))

# Application Configuration
APP_NAME = os.getenv("APP_NAME", "Keyword Rank Tracker")
APP_VERSION = os.getenv("APP_VERSION", "1.0.0")
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# Rate Limiting Configuration
API_RATE_LIMIT = int(os.getenv("API_RATE_LIMIT", "10"))  # requests per minute
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "50"))  # keywords per batch
REQUEST_DELAY = 60 / API_RATE_LIMIT  # seconds between requests

# Default Tracking Configuration
DEFAULT_COUNTRY = os.getenv("DEFAULT_COUNTRY", "US")
DEFAULT_LANGUAGE = os.getenv("DEFAULT_LANGUAGE", "en")
DEFAULT_DEVICE = os.getenv("DEFAULT_DEVICE", "desktop")

# Supported countries and languages
SUPPORTED_COUNTRIES = {
    "US": "United States",
    "UK": "United Kingdom", 
    "CA": "Canada",
    "AU": "Australia",
    "DE": "Germany",
    "FR": "France",
    "ES": "Spain",
    "IT": "Italy",
    "NL": "Netherlands",
    "BR": "Brazil",
    "IN": "India",
    "JP": "Japan",
    "AE": "United Arab Emirates"
}

SUPPORTED_LANGUAGES = {
    "en": "English",
    "es": "Spanish",
    "fr": "French",
    "de": "German",
    "it": "Italian",
    "pt": "Portuguese",
    "nl": "Dutch",
    "ja": "Japanese",
    "ar": "Arabic",
    "hi": "Hindi"
}

SUPPORTED_DEVICES = {
    "desktop": "Desktop",
    "mobile": "Mobile",
    "tablet": "Tablet"
}

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = os.getenv("LOG_FILE", str(LOGS_DIR / "app.log"))

# Export Configuration
EXPORT_FORMATS = ["CSV", "Excel"]
MAX_EXPORT_ROWS = 10000

# UI Configuration
MAX_KEYWORDS_DISPLAY = 1000
RESULTS_PER_PAGE = 50
CHART_HEIGHT = 400

# Tracking Configuration
MAX_KEYWORDS = 1000
MAX_POSITION_TRACK = 100
TRACKING_TIMEOUT = 300  # seconds
RETRY_ATTEMPTS = 3
