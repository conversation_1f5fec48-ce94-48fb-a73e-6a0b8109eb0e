"""
Keyword Rank Tracker - Main Streamlit Application
A comprehensive tool for tracking keyword rankings using Apify's SERP API
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ui.main_dashboard import main_dashboard
from src.ui.keyword_management import keyword_management_page
from src.ui.configuration import configuration_page
from src.ui.tracking_results import tracking_results_page
from src.database.db_manager import DatabaseManager
from src.utils.logger import setup_logger

# Configure Streamlit page
st.set_page_config(
    page_title="Keyword Rank Tracker",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize logger
logger = setup_logger()

def initialize_app():
    """Initialize the application and database"""
    try:
        # Initialize database
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Store database manager in session state
        if 'db_manager' not in st.session_state:
            st.session_state.db_manager = db_manager
            
        logger.info("Application initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        st.error(f"Failed to initialize application: {e}")
        return False

def main():
    """Main application entry point"""
    
    # Initialize app
    if not initialize_app():
        st.stop()
    
    # Sidebar navigation
    st.sidebar.title("🎯 Keyword Rank Tracker")
    st.sidebar.markdown("---")
    
    # Navigation menu
    page = st.sidebar.selectbox(
        "Navigate to:",
        [
            "📊 Dashboard",
            "🔧 Configuration", 
            "📝 Keyword Management",
            "📈 Tracking Results"
        ]
    )
    
    # Route to appropriate page
    if page == "📊 Dashboard":
        main_dashboard()
    elif page == "🔧 Configuration":
        configuration_page()
    elif page == "📝 Keyword Management":
        keyword_management_page()
    elif page == "📈 Tracking Results":
        tracking_results_page()
    
    # Footer
    st.sidebar.markdown("---")
    st.sidebar.markdown("**Keyword Rank Tracker v1.0**")
    st.sidebar.markdown("Built with Streamlit & Apify SERP API")

if __name__ == "__main__":
    main()
